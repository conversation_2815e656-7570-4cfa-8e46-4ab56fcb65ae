<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <!-- Official NuGet.org source -->
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    
    <!-- Nupack Internal NuGet Server (V3 API) -->
    <add key="Nupack Server" value="http://localhost:5003/v3/index.json" allowInsecureConnections="true" />

    <!-- Alternative V2 API endpoint for legacy compatibility -->
    <!-- <add key="Nupack Server V2" value="http://localhost:5003/api/v2" /> -->

    <!-- For production, use your actual server URL -->
    <!-- <add key="Nupack Server" value="https://nuget.yourcompany.com/v3/index.json" /> -->
  </packageSources>
  
  <!-- Optional: Set default push source -->
  <config>
    <add key="defaultPushSource" value="Nupack Server" />
  </config>
  
  <!-- Optional: Package source credentials (if authentication is added) -->
  <!--
  <packageSourceCredentials>
    <Nupack_x0020_Server>
      <add key="Username" value="your-username" />
      <add key="ClearTextPassword" value="your-api-key" />
    </Nupack_x0020_Server>
  </packageSourceCredentials>
  -->
</configuration>
