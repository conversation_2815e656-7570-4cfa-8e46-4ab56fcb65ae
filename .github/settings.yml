# Repository settings for Nupack.Server
# This file can be used with the Probot Settings app

repository:
  name: Nupack.Server
  description: "A modern, open-source NuGet V3 server implementation with web interface"
  homepage: https://github.com/dgknttr/Nupack.Server
  topics: 
    - nuget
    - dotnet
    - package-manager
    - aspnet-core
    - csharp
    - open-source
    - nuget-server
    - package-hosting
  private: true
  has_issues: true
  has_projects: true
  has_wiki: false
  has_downloads: true
  default_branch: main
  allow_squash_merge: true
  allow_merge_commit: false
  allow_rebase_merge: true
  delete_branch_on_merge: true
  enable_automated_security_fixes: true
  enable_vulnerability_alerts: true

# Labels for issues and pull requests
labels:
  - name: "bug"
    color: "d73a4a"
    description: "Something isn't working"
  
  - name: "enhancement"
    color: "a2eeef"
    description: "New feature or request"
  
  - name: "documentation"
    color: "0075ca"
    description: "Improvements or additions to documentation"
  
  - name: "good first issue"
    color: "7057ff"
    description: "Good for newcomers"
  
  - name: "help wanted"
    color: "008672"
    description: "Extra attention is needed"
  
  - name: "dependencies"
    color: "0366d6"
    description: "Pull requests that update a dependency file"
  
  - name: "security"
    color: "ee0701"
    description: "Security related issues"
  
  - name: "performance"
    color: "fbca04"
    description: "Performance improvements"
  
  - name: "api"
    color: "1d76db"
    description: "Related to API functionality"
  
  - name: "web-interface"
    color: "5319e7"
    description: "Related to web interface"
  
  - name: "docker"
    color: "0052cc"
    description: "Docker related changes"
  
  - name: "ci/cd"
    color: "28a745"
    description: "Continuous integration and deployment"

# Branch protection rules
branches:
  - name: main
    protection:
      required_status_checks:
        strict: true
        contexts:
          - "Test"
          - "Security Scan"
          - "CodeQL Analysis (csharp)"
      enforce_admins: true
      required_pull_request_reviews:
        required_approving_review_count: 1
        dismiss_stale_reviews: true
        require_code_owner_reviews: true
        require_last_push_approval: true
      restrictions: null
      required_linear_history: true
      allow_force_pushes: false
      allow_deletions: false
