## Description
Brief description of the changes in this PR.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring
- [ ] Security fix

## Related Issues
Fixes #(issue number)
Closes #(issue number)
Related to #(issue number)

## Changes Made
- [ ] Change 1
- [ ] Change 2
- [ ] Change 3

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed
- [ ] All existing tests pass

### Test Coverage
- [ ] New code is covered by tests
- [ ] Test coverage percentage maintained or improved

## Documentation
- [ ] Code comments added/updated
- [ ] README updated
- [ ] API documentation updated
- [ ] Configuration documentation updated

## Security Considerations
- [ ] No sensitive data exposed
- [ ] Input validation implemented
- [ ] Authentication/authorization considered
- [ ] Security scan passed

## Performance Impact
- [ ] No performance regression
- [ ] Performance improvement measured
- [ ] Memory usage considered
- [ ] Database query optimization (if applicable)

## Breaking Changes
If this is a breaking change, please describe:
- What breaks
- Migration path for users
- Version bump required

## Screenshots (if applicable)
Add screenshots to help explain your changes.

## Checklist
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## Additional Notes
Any additional information that reviewers should know.
