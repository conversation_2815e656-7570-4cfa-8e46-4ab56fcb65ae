<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <AssemblyName>Nupack.Server.Api</AssemblyName>
    <RootNamespace>Nupack.Server.Api</RootNamespace>
    <Product>Nupack Server API</Product>
    <InternalsVisibleTo>Nupack.Server.Tests</InternalsVisibleTo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="7.2.0" />
    <PackageReference Include="System.IO.Compression.ZipFile" Version="4.3.0" />
    <PackageReference Include="NuGet.Packaging" Version="6.11.1" />
    <PackageReference Include="NuGet.Versioning" Version="6.11.1" />
  </ItemGroup>

</Project>
