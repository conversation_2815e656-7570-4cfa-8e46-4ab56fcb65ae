{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "NuGetServer": {
    "BaseUrl": "https://your-nuget-server.com",
    "Name": "Your Company NuGet"
  },
  "Branding": {
    "CompanyName": "Your Company Name",
    "NugetSourceUrl": "https://your-nuget-server.com/v3/index.json"
  },
  "PackageStorage": {
    "BasePath": null
  }
}

// Example configurations for different deployment scenarios:

// Internal Server
/*
{
  "Branding": {
    "CompanyName": "Internal Team",
    "NugetSourceUrl": "https://nuget.internal.example.com/v3/index.json"
  },
  "PackageStorage": {
    "BasePath": "/var/nuget/packages"
  }
}
*/

// Public Server
/*
{
  "Branding": {
    "CompanyName": "Open Source Project",
    "NugetSourceUrl": "https://packages.myproject.org/v3/index.json"
  },
  "PackageStorage": {
    "BasePath": "${NUGET_PACKAGES_PATH}"
  }
}
*/

// Development Server
/*
{
  "Branding": {
    "CompanyName": "Development Team",
    "NugetSourceUrl": "https://nuget-dev.example.com/v3/index.json"
  },
  "PackageStorage": {
    "BasePath": "data/packages"
  }
}
*/

// Package Storage Configuration Examples:
//
// 1. Default (null): Uses "packages" folder in WebRootPath or ContentRootPath
//    "PackageStorage": { "BasePath": null }
//
// 2. Relative path: Relative to application's ContentRootPath
//    "PackageStorage": { "BasePath": "data/packages" }
//
// 3. Absolute path: Direct path specification
//    "PackageStorage": { "BasePath": "/var/nuget/packages" }
//    "PackageStorage": { "BasePath": "C:\\NuGet\\Packages" }
//
// 4. Environment variable: Use ${VARIABLE_NAME} syntax
//    "PackageStorage": { "BasePath": "${NUGET_PACKAGES_PATH}" }
//    "PackageStorage": { "BasePath": "${HOME}/nuget-packages" }
//
// 5. Docker volume mount example:
//    "PackageStorage": { "BasePath": "/app/packages" }
//
// Cross-platform considerations:
// - Use forward slashes (/) for paths - they work on all platforms
// - Environment variables are expanded automatically
// - Paths are normalized for the target platform
