@model dynamic

@{
    var versions = Model.Versions as List<Nupack.Server.Web.Models.RegistrationLeaf> ?? new List<Nupack.Server.Web.Models.RegistrationLeaf>();
    var packageId = Model.PackageId as string ?? "";
    var selectedVersion = Model.SelectedVersion as string ?? "";
    var showInstallCommands = Model.ShowInstallCommands ?? true;
    var maxVersionsToShow = Model.MaxVersionsToShow ?? 10;
    var showExpandButton = versions.Count > maxVersionsToShow;
}

<div class="version-list">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">
            Available Versions (@versions.Count)
        </h3>
        @if (showExpandButton)
        {
            <button id="toggle-versions" onclick="toggleVersions()" class="text-sm text-nuget-blue hover:text-nuget-blue-dark transition-colors duration-200">
                <span id="toggle-text">Show all</span>
                <svg id="toggle-icon" class="w-4 h-4 inline ml-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
        }
    </div>

    <div class="space-y-3">
        @for (int i = 0; i < versions.Count; i++)
        {
            var version = versions[i];
            var catalogEntry = version.CatalogEntry;
            var isSelected = catalogEntry.Version == selectedVersion;
            var isLatest = i == 0;
            var isHidden = i >= maxVersionsToShow && showExpandButton;

            <div class="version-item @(isHidden ? "hidden" : "") @(isSelected ? "selected" : "")" 
                 data-version="@catalogEntry.Version">
                <div class="border @(isSelected ? "border-nuget-blue bg-blue-50" : "border-gray-200 hover:border-gray-300") rounded-lg p-4 transition-all duration-200 cursor-pointer"
                     onclick="selectVersion('@catalogEntry.Version')">
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <!-- Version Info -->
                            <div>
                                <div class="flex items-center space-x-2">
                                    <span class="font-medium text-gray-900"><EMAIL></span>
                                    
                                    @if (catalogEntry.IsPrerelease)
                                    {
                                        <span class="badge-warning">Prerelease</span>
                                    }
                                    
                                    @if (isLatest)
                                    {
                                        <span class="badge-success">Latest</span>
                                    }
                                    
                                    @if (isSelected)
                                    {
                                        <span class="badge-primary">Selected</span>
                                    }
                                </div>
                                
                                <div class="text-sm text-gray-500 mt-1">
                                    Published @catalogEntry.Published.ToString("MMM dd, yyyy")
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center space-x-2">
                            @if (showInstallCommands)
                            {
                                <button onclick="event.stopPropagation(); copyVersionCommand('@packageId', '@catalogEntry.Version')" 
                                        class="p-2 text-gray-400 hover:text-nuget-blue transition-colors duration-200" 
                                        title="Copy install command">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </button>
                            }
                            
                            <div class="text-gray-400">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Version Details (shown when selected) -->
                    @if (isSelected && !string.IsNullOrEmpty(catalogEntry.Description))
                    {
                        <div class="mt-3 pt-3 border-t border-gray-200">
                            <p class="text-sm text-gray-600">@catalogEntry.Description</p>
                            
                            @if (!string.IsNullOrEmpty(catalogEntry.ReleaseNotes))
                            {
                                <div class="mt-2">
                                    <h5 class="text-sm font-medium text-gray-900 mb-1">Release Notes</h5>
                                    <p class="text-sm text-gray-600">@catalogEntry.ReleaseNotes</p>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        }
    </div>

    @if (!versions.Any())
    {
        <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No versions available</h3>
            <p class="text-gray-600">This package doesn't have any published versions yet.</p>
        </div>
    }
</div>

<!-- Install Command Modal/Section for Selected Version -->
@if (showInstallCommands && !string.IsNullOrEmpty(selectedVersion))
{
    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Install Selected Version</h4>
        <partial name="_InstallCommand" model="@(new { PackageId = packageId, Version = selectedVersion, ShowMultipleCommands = true })" />
    </div>
}

<script>
    let versionsExpanded = false;

    function toggleVersions() {
        const hiddenVersions = document.querySelectorAll('.version-item.hidden');
        const toggleText = document.getElementById('toggle-text');
        const toggleIcon = document.getElementById('toggle-icon');
        
        versionsExpanded = !versionsExpanded;
        
        hiddenVersions.forEach(version => {
            if (versionsExpanded) {
                version.classList.remove('hidden');
            } else {
                version.classList.add('hidden');
            }
        });
        
        toggleText.textContent = versionsExpanded ? 'Show less' : 'Show all';
        toggleIcon.style.transform = versionsExpanded ? 'rotate(180deg)' : 'rotate(0deg)';
    }

    function selectVersion(version) {
        // Remove selected class from all versions
        document.querySelectorAll('.version-item').forEach(item => {
            item.classList.remove('selected');
            const container = item.querySelector('.border');
            container.classList.remove('border-nuget-blue', 'bg-blue-50');
            container.classList.add('border-gray-200');
        });
        
        // Add selected class to clicked version
        const selectedItem = document.querySelector(`[data-version="${version}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
            const container = selectedItem.querySelector('.border');
            container.classList.remove('border-gray-200');
            container.classList.add('border-nuget-blue', 'bg-blue-50');
        }
        
        // Update URL or trigger page update
        const url = new URL(window.location);
        url.searchParams.set('version', version);
        window.history.pushState({}, '', url);
        
        // Optionally reload the page to show updated install commands
        // window.location.reload();
    }

    function copyVersionCommand(packageId, version) {
        const command = `dotnet add package ${packageId} --version ${version} --source "Nupack Server"`;
        navigator.clipboard.writeText(command).then(() => {
            showToast(`Install command for v${version} copied to clipboard!`, 'success');
        }).catch(() => {
            showToast('Failed to copy command', 'error');
        });
    }

    function showToast(message, type) {
        // Remove existing toasts
        const existingToasts = document.querySelectorAll('.toast-notification');
        existingToasts.forEach(toast => toast.remove());

        const toast = document.createElement('div');
        toast.className = `toast-notification fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white font-medium transition-all duration-300 transform translate-x-full ${
            type === 'success' ? 'bg-green-500' : 'bg-red-500'
        }`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        // Animate out and remove
        setTimeout(() => {
            toast.style.transform = 'translateX(full)';
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // Initialize selected version from URL
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const versionParam = urlParams.get('version');
        if (versionParam) {
            selectVersion(versionParam);
        }
    });
</script>
