@model dynamic

@{
    var version = Model.Version as Nupack.Server.Web.Models.RegistrationLeaf;
    var packageId = Model.PackageId as string ?? "";
    var selectedVersion = Model.SelectedVersion as string ?? "";
    var isCompact = Model.IsCompact ?? false;
    
    if (version == null) return;
    
    var catalogEntry = version.CatalogEntry;
    var isSelected = catalogEntry.Version == selectedVersion;
    var isLatest = Model.IsLatest ?? false;
}

<div class="border @(isSelected ? "border-blue-500 bg-blue-100" : "border-gray-200 hover:border-gray-300") rounded-lg @(isCompact ? "p-2" : "p-3") transition-all duration-200 cursor-pointer">
    <div class="flex items-center justify-between">
        <div class="flex-1">
            <div class="flex items-center space-x-2 @(isCompact ? "mb-0.5" : "mb-1")">
                <span class="@(isCompact ? "text-sm" : "font-medium") text-gray-900"><EMAIL></span>
                
                @if (catalogEntry.IsPrerelease)
                {
                    <span class="inline-flex items-center px-1.5 py-0.5 rounded @(isCompact ? "text-xs" : "text-xs") font-medium bg-yellow-100 text-yellow-800">
                        @if (!isCompact)
                        {
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        }
                        Prerelease
                    </span>
                }
                
                @if (isLatest)
                {
                    <span class="inline-flex items-center px-1.5 py-0.5 rounded @(isCompact ? "text-xs" : "text-xs") font-medium bg-green-100 text-green-800">
                        @if (!isCompact)
                        {
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        }
                        Latest
                    </span>
                }
                
                @if (isSelected)
                {
                    <span class="inline-flex items-center px-1.5 py-0.5 rounded @(isCompact ? "text-xs" : "text-xs") font-medium bg-blue-100 text-blue-800">
                        @if (!isCompact)
                        {
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        }
                        Selected
                    </span>
                }
            </div>
            
            <div class="@(isCompact ? "text-xs" : "text-xs") text-gray-500">
                <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a1 1 0 011 1v9a2 2 0 01-2 2H5a2 2 0 01-2-2V8a1 1 0 011-1h3z"></path>
                </svg>
                @catalogEntry.Published.ToString("MMM dd, yyyy")
            </div>
            
            @if (!isCompact && !string.IsNullOrEmpty(catalogEntry.ReleaseNotes))
            {
                <div class="mt-2 text-xs text-gray-600 line-clamp-2">
                    @catalogEntry.ReleaseNotes
                </div>
            }
        </div>
        
        <div class="flex items-center space-x-2 ml-3">
            @if (!isSelected)
            {
                <a asp-page="/Packages/Details" asp-route-id="@packageId" asp-route-version="@catalogEntry.Version"
                   class="@(isCompact ? "text-xs" : "text-sm") text-gray-400 hover:text-nuget-blue transition-colors duration-200 font-medium"
                   title="Select this version">
                    Select
                </a>
            }
            
            @if (!isCompact)
            {
                <button onclick="copyVersionCommand('@packageId', '@catalogEntry.Version')" 
                        class="p-1 text-gray-400 hover:text-nuget-blue transition-colors duration-200" 
                        title="Copy install command for this version"
                        aria-label="Copy install command for version @catalogEntry.Version">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </button>
            }
        </div>
    </div>
</div>

<script>
    function copyVersionCommand(packageId, version) {
        const command = `dotnet add package ${packageId} --version ${version} --source "Nupack Server"`;
        navigator.clipboard.writeText(command).then(() => {
            showToast(`Install command for ${packageId} v${version} copied!`, 'success');
        }).catch(() => {
            showToast('Failed to copy command', 'error');
        });
    }

    function showToast(message, type) {
        // Remove existing toasts
        const existingToasts = document.querySelectorAll('.toast-notification');
        existingToasts.forEach(toast => toast.remove());

        const toast = document.createElement('div');
        toast.className = `toast-notification fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white font-medium transition-all duration-300 transform translate-x-full ${
            type === 'success' ? 'bg-green-500' : 'bg-red-500'
        }`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        // Animate out and remove
        setTimeout(() => {
            toast.style.transform = 'translateX(full)';
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
</script>
