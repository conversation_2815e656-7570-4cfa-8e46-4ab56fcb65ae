@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2;
  }
  
  .btn-outline {
    @apply border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 transition-shadow duration-200 hover:shadow-md;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900 placeholder-gray-500;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-blue-100 text-blue-800;
  }
  
  .badge-secondary {
    @apply badge bg-gray-100 text-gray-800;
  }
  
  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }
  
  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }
  
  .badge-danger {
    @apply badge bg-red-100 text-red-800;
  }
  
  .code-block {
    @apply bg-gray-100 border border-gray-200 rounded-lg p-3 font-mono text-sm text-gray-800 overflow-x-auto;
  }

  .command-block {
    @apply overflow-auto whitespace-nowrap rounded bg-gray-50 px-4 py-2 text-sm font-mono text-gray-800 border border-gray-200;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    scrollbar-width: thin; /* Firefox */
  }

  .command-block::-webkit-scrollbar {
    height: 4px;
  }

  .command-block::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded;
  }

  .command-block::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded;
  }

  .command-block::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
  
  .copy-button {
    @apply ml-2 text-gray-500 hover:text-blue-600 cursor-pointer transition-colors duration-200;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Loading animation */
.loading-spinner {
  @apply inline-block w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin;
}

/* Package card hover effects */
.package-card {
  @apply transition-all duration-200 hover:-translate-y-1 hover:shadow-lg;
}

/* Search input focus ring */
.search-input:focus {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

/* Responsive text utilities */
.text-responsive-lg {
  @apply text-lg sm:text-xl md:text-2xl;
}

.text-responsive-xl {
  @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl;
}
